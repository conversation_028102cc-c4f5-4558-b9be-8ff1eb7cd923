[{"test_name": "throughput_llama8B_tp1", "environment_variables": {"VLLM_ALLOW_LONG_MAX_MODEL_LEN": 1, "VLLM_CPU_KVCACHE_SPACE": 40}, "parameters": {"model": "meta-llama/Meta-Llama-3.1-8B-Instruct", "tensor_parallel_size": 1, "load_format": "dummy", "dataset": "./ShareGPT_V3_unfiltered_cleaned_split.json", "num_prompts": 200, "backend": "vllm"}}, {"test_name": "throughput_llama8B_tp4", "environment_variables": {"VLLM_ALLOW_LONG_MAX_MODEL_LEN": 1, "VLLM_CPU_KVCACHE_SPACE": 40}, "parameters": {"model": "meta-llama/Meta-Llama-3.1-8B-Instruct", "tensor_parallel_size": 4, "load_format": "dummy", "dataset": "./ShareGPT_V3_unfiltered_cleaned_split.json", "num_prompts": 200, "backend": "vllm"}}]