<!-- Enables the use of toc_depth in document frontmatter https://github.com/squidfunk/mkdocs-material/issues/4827#issuecomment-1869812019 -->
<li class="md-nav__item">
    <a href="{{ toc_item.url }}" class="md-nav__link">
      <span class="md-ellipsis">
        {{ toc_item.title }}
      </span>
    </a>
  
    <!-- Table of contents list -->
    {% if toc_item.children %}
      <nav class="md-nav" aria-label="{{ toc_item.title | striptags }}">
        <ul class="md-nav__list">
          {% for toc_item in toc_item.children %}
          {% if not page.meta.toc_depth or toc_item.level <= page.meta.toc_depth %}
            {% include "partials/toc-item.html" %}
          {% endif %}
          {% endfor %}
        </ul>
      </nav>
    {% endif %}
  </li>